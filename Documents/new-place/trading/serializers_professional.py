"""
Professional trading serializers for order management and execution.
"""
from rest_framework import serializers
from decimal import Decimal
from .models_professional import Instrument, Order, Execution, Position, MarketTick
from wallets.models import TradingAccount


class InstrumentSerializer(serializers.ModelSerializer):
    """Instrument serializer."""
    
    class Meta:
        model = Instrument
        fields = (
            'id', 'symbol', 'name', 'instrument_type', 'base_currency', 'quote_currency',
            'min_quantity', 'max_quantity', 'tick_size', 'is_active', 'created_at', 'updated_at'
        )
        read_only_fields = ('id', 'created_at', 'updated_at')


class OrderCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating orders."""

    account_id = serializers.UUIDField(write_only=True)
    instrument_id = serializers.UUIDField(write_only=True, required=False)
    asset_id = serializers.UUIDField(write_only=True, required=False, help_text="Legacy asset ID for compatibility")
    
    class Meta:
        model = Order
        fields = (
            'account_id', 'instrument_id', 'asset_id', 'order_type', 'side', 'quantity',
            'price', 'stop_price', 'time_in_force', 'expires_at', 'leverage'
        )
    
    def validate(self, attrs):
        """Validate order data."""
        user = self.context['request'].user

        # Validate account ownership
        try:
            account = TradingAccount.objects.get(
                id=attrs['account_id'],
                user=user,
                status='ACTIVE'
            )
        except TradingAccount.DoesNotExist:
            raise serializers.ValidationError({"account_id": "Trading account not found or not accessible."})

        # Handle both instrument_id and asset_id (for compatibility)
        instrument = None
        if attrs.get('instrument_id'):
            try:
                instrument = Instrument.objects.get(
                    id=attrs['instrument_id'],
                    is_active=True
                )
            except Instrument.DoesNotExist:
                raise serializers.ValidationError({"instrument_id": "Instrument not found or not active."})
        elif attrs.get('asset_id'):
            # Try to find instrument by legacy asset ID
            try:
                instrument = Instrument.objects.get(
                    legacy_asset_id=attrs['asset_id'],
                    is_active=True
                )
            except Instrument.DoesNotExist:
                # If not found by legacy ID, try to find by asset symbol
                from trading.models import Asset
                try:
                    asset = Asset.objects.get(id=attrs['asset_id'], is_active=True)
                    instrument = Instrument.objects.get(symbol=asset.symbol, is_active=True)
                except (Asset.DoesNotExist, Instrument.DoesNotExist):
                    raise serializers.ValidationError({"asset_id": "Asset/Instrument not found or not active."})
        else:
            raise serializers.ValidationError("Either instrument_id or asset_id must be provided.")
        
        # Validate quantity limits
        if attrs['quantity'] < instrument.min_quantity:
            raise serializers.ValidationError({"quantity": f"Minimum quantity is {instrument.min_quantity}"})
        
        if attrs['quantity'] > instrument.max_quantity:
            raise serializers.ValidationError({"quantity": f"Maximum quantity is {instrument.max_quantity}"})
        
        # Validate price for limit orders
        if attrs['order_type'] in ['LIMIT', 'STOP_LIMIT'] and not attrs.get('price'):
            raise serializers.ValidationError({"price": "Price is required for limit orders."})
        
        # Validate stop price for stop orders
        if attrs['order_type'] in ['STOP', 'STOP_LIMIT'] and not attrs.get('stop_price'):
            raise serializers.ValidationError({"stop_price": "Stop price is required for stop orders."})
        
        # Validate account balance for margin calculation
        margin_required = self._calculate_margin_required(attrs, account, instrument)
        if account.balance < margin_required:
            raise serializers.ValidationError({"quantity": "Insufficient account balance for this order."})
        
        attrs['account'] = account
        attrs['instrument'] = instrument
        attrs['margin_required'] = margin_required
        
        return attrs
    
    def _calculate_margin_required(self, attrs, account, instrument):
        """Calculate margin required for the order."""
        quantity = attrs['quantity']
        leverage = attrs.get('leverage', 1)
        
        # For market orders, use current market price estimate
        # For limit orders, use the limit price
        if attrs['order_type'] == 'MARKET':
            # Get latest market price (simplified - in real system would use order book)
            latest_tick = MarketTick.objects.filter(instrument=instrument).first()
            estimated_price = latest_tick.last_price if latest_tick else Decimal('100')
        else:
            estimated_price = attrs.get('price', Decimal('100'))
        
        notional_value = quantity * estimated_price
        margin_required = notional_value / leverage
        
        return margin_required
    
    def create(self, validated_data):
        """Create a new order."""
        # Remove helper fields
        account = validated_data.pop('account')
        instrument = validated_data.pop('instrument')
        validated_data.pop('account_id')
        validated_data.pop('instrument_id', None)  # May not exist if asset_id was used
        validated_data.pop('asset_id', None)  # May not exist if instrument_id was used
        
        # Create the order
        order = Order.objects.create(
            account=account,
            instrument=instrument,
            **validated_data
        )
        
        return order


class OrderSerializer(serializers.ModelSerializer):
    """Order serializer for reading."""
    
    account = serializers.StringRelatedField()
    instrument = InstrumentSerializer(read_only=True)
    remaining_quantity = serializers.ReadOnlyField()
    is_fully_filled = serializers.ReadOnlyField()
    fill_percentage = serializers.ReadOnlyField()
    
    class Meta:
        model = Order
        fields = (
            'id', 'account', 'instrument', 'order_type', 'side', 'quantity', 'price', 
            'stop_price', 'filled_quantity', 'remaining_quantity', 'average_fill_price', 
            'status', 'time_in_force', 'expires_at', 'leverage', 'margin_required',
            'is_fully_filled', 'fill_percentage', 'created_at', 'updated_at'
        )


class ExecutionSerializer(serializers.ModelSerializer):
    """Execution serializer."""

    order = serializers.StringRelatedField()
    account = serializers.StringRelatedField()
    instrument = InstrumentSerializer(read_only=True)
    notional_value = serializers.ReadOnlyField()
    net_amount = serializers.ReadOnlyField()

    class Meta:
        model = Execution
        fields = (
            'id', 'order', 'account', 'instrument', 'side', 'price', 'quantity',
            'fee', 'fee_currency', 'notional_value', 'net_amount', 'market_price', 'executed_at'
        )


class PositionSerializer(serializers.ModelSerializer):
    """Position serializer."""
    
    account = serializers.StringRelatedField()
    instrument = InstrumentSerializer(read_only=True)
    total_pnl = serializers.ReadOnlyField()
    pnl_percentage = serializers.ReadOnlyField()
    
    class Meta:
        model = Position
        fields = (
            'id', 'account', 'instrument', 'side', 'entry_price', 'quantity', 
            'current_price', 'unrealized_pnl', 'realized_pnl', 'total_pnl', 
            'pnl_percentage', 'leverage', 'margin_used', 'status', 'stop_loss', 
            'take_profit', 'opened_at', 'closed_at'
        )


class MarketTickSerializer(serializers.ModelSerializer):
    """Market tick serializer."""
    
    instrument = InstrumentSerializer(read_only=True)
    spread = serializers.ReadOnlyField()
    mid_price = serializers.ReadOnlyField()
    
    class Meta:
        model = MarketTick
        fields = (
            'id', 'instrument', 'timestamp', 'bid', 'ask', 'last_price', 
            'volume', 'spread', 'mid_price'
        )


class OrderCancelSerializer(serializers.Serializer):
    """Serializer for cancelling orders."""
    
    reason = serializers.CharField(max_length=255, required=False, default="User cancelled")
    
    def validate(self, attrs):
        """Validate cancellation request."""
        order = self.context['order']
        
        if order.status not in ['PENDING', 'PARTIALLY_FILLED']:
            raise serializers.ValidationError("Only pending or partially filled orders can be cancelled.")
        
        return attrs


class PositionCloseSerializer(serializers.Serializer):
    """Serializer for closing positions."""
    
    quantity = serializers.DecimalField(max_digits=20, decimal_places=8, required=False)
    reason = serializers.CharField(max_length=255, required=False, default="User closed")
    
    def validate(self, attrs):
        """Validate position close request."""
        position = self.context['position']
        
        if position.status != 'OPEN':
            raise serializers.ValidationError("Only open positions can be closed.")
        
        # If quantity not specified, close entire position
        if 'quantity' not in attrs:
            attrs['quantity'] = position.quantity
        elif attrs['quantity'] > position.quantity:
            raise serializers.ValidationError("Cannot close more than the position quantity.")
        
        return attrs
