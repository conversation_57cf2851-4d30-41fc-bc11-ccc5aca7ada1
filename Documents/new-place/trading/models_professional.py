"""
Professional trading models for order management, executions, and positions.
This replaces the simple Trade model with a proper trading system.
"""
import uuid
from decimal import Decimal
from django.db import models
from django.conf import settings
from django.utils import timezone
from wallets.models import TradingAccount


class Instrument(models.Model):
    """Financial instrument (replaces Asset for trading)."""
    
    INSTRUMENT_TYPES = (
        ('STOCK', 'Stock'),
        ('FOREX', 'Forex'),
        ('CRYPTO', 'Cryptocurrency'),
        ('COMMODITY', 'Commodity'),
        ('INDEX', 'Index'),
        ('OPTION', 'Option'),
        ('FUTURE', 'Future'),
    )
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    symbol = models.CharField(max_length=20, unique=True)
    name = models.Char<PERSON><PERSON>(max_length=100)
    instrument_type = models.CharField(max_length=20, choices=INSTRUMENT_TYPES)
    base_currency = models.Cha<PERSON><PERSON><PERSON>(max_length=10)  # e.g., 'BTC' in BTC/USDT
    quote_currency = models.CharField(max_length=10)  # e.g., 'USDT' in BTC/USDT
    min_quantity = models.DecimalField(max_digits=20, decimal_places=8, default=Decimal('0.********'))
    max_quantity = models.DecimalField(max_digits=20, decimal_places=8, default=Decimal('1000000'))
    tick_size = models.DecimalField(max_digits=20, decimal_places=8, default=Decimal('0.01'))  # Minimum price increment
    is_active = models.BooleanField(default=True)
    # Link to legacy Asset model for compatibility
    legacy_asset_id = models.UUIDField(null=True, blank=True, help_text="Link to legacy Asset model")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['symbol']

    def __str__(self):
        return f"{self.symbol} ({self.name})"


class Order(models.Model):
    """Order represents a trading order (buy/sell instruction)."""
    
    ORDER_TYPES = (
        ('MARKET', 'Market Order'),
        ('LIMIT', 'Limit Order'),
        ('STOP', 'Stop Order'),
        ('STOP_LIMIT', 'Stop Limit Order'),
    )
    
    SIDES = (
        ('BUY', 'Buy'),
        ('SELL', 'Sell'),
    )
    
    STATUSES = (
        ('PENDING', 'Pending'),
        ('FILLED', 'Filled'),
        ('PARTIALLY_FILLED', 'Partially Filled'),
        ('CANCELLED', 'Cancelled'),
        ('REJECTED', 'Rejected'),
        ('EXPIRED', 'Expired'),
        ('CLOSED', 'Closed'),
    )
    
    TIME_IN_FORCE = (
        ('GTC', 'Good Till Cancelled'),
        ('IOC', 'Immediate or Cancel'),
        ('FOK', 'Fill or Kill'),
        ('GTD', 'Good Till Date'),
    )
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    account = models.ForeignKey(TradingAccount, on_delete=models.CASCADE, related_name='orders')
    instrument = models.ForeignKey(Instrument, on_delete=models.CASCADE, related_name='orders')
    order_type = models.CharField(max_length=20, choices=ORDER_TYPES)
    side = models.CharField(max_length=10, choices=SIDES)
    quantity = models.DecimalField(max_digits=20, decimal_places=8)
    price = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)  # For limit orders
    stop_price = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)  # For stop orders
    filled_quantity = models.DecimalField(max_digits=20, decimal_places=8, default=0)
    average_fill_price = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUSES, default='PENDING')
    time_in_force = models.CharField(max_length=10, choices=TIME_IN_FORCE, default='GTC')
    expires_at = models.DateTimeField(null=True, blank=True)  # For GTD orders
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Additional fields for risk management
    leverage = models.DecimalField(max_digits=10, decimal_places=2, default=1)
    margin_required = models.DecimalField(max_digits=20, decimal_places=8, default=0)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.side} {self.quantity} {self.instrument.symbol} @ {self.price or 'MARKET'}"
    
    @property
    def remaining_quantity(self):
        """Calculate remaining quantity to be filled."""
        return self.quantity - self.filled_quantity
    
    @property
    def is_fully_filled(self):
        """Check if order is fully filled."""
        return self.filled_quantity >= self.quantity
    
    @property
    def fill_percentage(self):
        """Calculate fill percentage."""
        if self.quantity == 0:
            return 0
        return float(self.filled_quantity / self.quantity * 100)


class Execution(models.Model):
    """Execution represents an execution/fill of an order."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='executions')
    account = models.ForeignKey(TradingAccount, on_delete=models.CASCADE, related_name='executions')
    instrument = models.ForeignKey(Instrument, on_delete=models.CASCADE, related_name='executions')
    side = models.CharField(max_length=10, choices=Order.SIDES)
    price = models.DecimalField(max_digits=20, decimal_places=8)
    quantity = models.DecimalField(max_digits=20, decimal_places=8)
    fee = models.DecimalField(max_digits=20, decimal_places=8, default=0)
    fee_currency = models.CharField(max_length=10, default='USD')
    executed_at = models.DateTimeField(auto_now_add=True)
    
    # Market data at execution time
    market_price = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    
    class Meta:
        ordering = ['-executed_at']
    
    def __str__(self):
        return f"Execution: {self.side} {self.quantity} {self.instrument.symbol} @ {self.price}"
    
    @property
    def notional_value(self):
        """Calculate notional value of the trade."""
        return self.price * self.quantity
    
    @property
    def net_amount(self):
        """Calculate net amount after fees."""
        return self.notional_value - self.fee


class Position(models.Model):
    """Position tracks ongoing positions for margin trading."""
    
    SIDES = (
        ('LONG', 'Long'),
        ('SHORT', 'Short'),
    )
    
    STATUSES = (
        ('OPEN', 'Open'),
        ('CLOSED', 'Closed'),
    )
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    account = models.ForeignKey(TradingAccount, on_delete=models.CASCADE, related_name='positions')
    instrument = models.ForeignKey(Instrument, on_delete=models.CASCADE, related_name='positions')
    side = models.CharField(max_length=10, choices=SIDES)
    entry_price = models.DecimalField(max_digits=20, decimal_places=8)
    quantity = models.DecimalField(max_digits=20, decimal_places=8)
    current_price = models.DecimalField(max_digits=20, decimal_places=8)
    unrealized_pnl = models.DecimalField(max_digits=20, decimal_places=8, default=0)
    realized_pnl = models.DecimalField(max_digits=20, decimal_places=8, default=0)
    leverage = models.DecimalField(max_digits=10, decimal_places=2, default=1)
    margin_used = models.DecimalField(max_digits=20, decimal_places=8, default=0)
    status = models.CharField(max_length=10, choices=STATUSES, default='OPEN')
    opened_at = models.DateTimeField(auto_now_add=True)
    closed_at = models.DateTimeField(null=True, blank=True)
    
    # Risk management
    stop_loss = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    take_profit = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    
    class Meta:
        ordering = ['-opened_at']
        unique_together = ['account', 'instrument', 'status']  # One open position per instrument per account
    
    def __str__(self):
        return f"{self.side} {self.quantity} {self.instrument.symbol} @ {self.entry_price}"
    
    def update_unrealized_pnl(self, current_price):
        """Update unrealized PnL based on current market price."""
        self.current_price = current_price
        
        if self.side == 'LONG':
            self.unrealized_pnl = (current_price - self.entry_price) * self.quantity
        else:  # SHORT
            self.unrealized_pnl = (self.entry_price - current_price) * self.quantity
        
        self.save(update_fields=['current_price', 'unrealized_pnl'])
    
    @property
    def total_pnl(self):
        """Calculate total PnL (realized + unrealized)."""
        return self.realized_pnl + self.unrealized_pnl
    
    @property
    def pnl_percentage(self):
        """Calculate PnL percentage."""
        if self.entry_price == 0:
            return 0
        return float(self.total_pnl / (self.entry_price * self.quantity) * 100)


class MarketTick(models.Model):
    """Market data tick for real-time pricing."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    instrument = models.ForeignKey(Instrument, on_delete=models.CASCADE, related_name='ticks')
    timestamp = models.DateTimeField(auto_now_add=True)
    bid = models.DecimalField(max_digits=20, decimal_places=8)
    ask = models.DecimalField(max_digits=20, decimal_places=8)
    last_price = models.DecimalField(max_digits=20, decimal_places=8)
    volume = models.DecimalField(max_digits=20, decimal_places=8, default=0)
    
    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['instrument', '-timestamp']),
        ]
    
    def __str__(self):
        return f"{self.instrument.symbol} @ {self.last_price} ({self.timestamp})"
    
    @property
    def spread(self):
        """Calculate bid-ask spread."""
        return self.ask - self.bid
    
    @property
    def mid_price(self):
        """Calculate mid price."""
        return (self.bid + self.ask) / 2
