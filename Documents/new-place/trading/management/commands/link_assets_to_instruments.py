from django.core.management.base import BaseCommand
from trading.models_professional import Instrument
from trading.models import Asset


class Command(BaseCommand):
    help = 'Link existing assets to instruments for compatibility'

    def handle(self, *args, **options):
        self.stdout.write('Linking assets to instruments...')
        
        linked_count = 0
        assets = Asset.objects.filter(is_active=True)
        
        for asset in assets:
            instruments = Instrument.objects.filter(symbol=asset.symbol)
            if instruments.exists():
                instrument = instruments.first()
                if not instrument.legacy_asset_id:
                    instrument.legacy_asset_id = asset.id
                    instrument.save()
                    linked_count += 1
                    self.stdout.write(f"  Linked {asset.symbol} (Asset: {asset.id}) -> (Instrument: {instrument.id})")
                else:
                    self.stdout.write(f"  Already linked: {asset.symbol}")
            else:
                self.stdout.write(f"  Warning: No instrument found for asset {asset.symbol}")
        
        self.stdout.write(
            self.style.SUCCESS(f"Linked {linked_count} assets to instruments")
        )
