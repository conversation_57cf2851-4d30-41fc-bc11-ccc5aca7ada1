from django.core.management.base import BaseCommand
from django.db import transaction
from decimal import Decimal
from trading.models_professional import Instrument, MarketTick
from trading.models import Asset


class Command(BaseCommand):
    help = 'Setup professional trading system with instruments and market data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-instruments',
            action='store_true',
            help='Create instruments from existing assets',
        )
        parser.add_argument(
            '--create-market-data',
            action='store_true',
            help='Create sample market data',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up professional trading system...'))
        
        if options['create_instruments']:
            self.create_instruments()
        
        if options['create_market_data']:
            self.create_market_data()
        
        self.stdout.write(self.style.SUCCESS('Professional trading setup completed!'))

    def create_instruments(self):
        """Create instruments from existing assets."""
        self.stdout.write('Creating instruments from assets...')
        
        assets = Asset.objects.filter(is_active=True)
        created_count = 0
        
        for asset in assets:
            # Parse currency pairs for forex and crypto
            if asset.asset_type in ['FOREX', 'CRYPTO'] and '/' in asset.symbol:
                base_currency, quote_currency = asset.symbol.split('/')
                if quote_currency == 'USDT':
                    quote_currency = 'USD'  # Normalize USDT to USD
            else:
                base_currency = asset.symbol
                quote_currency = 'USD'
            
            # Set appropriate min/max quantities and tick sizes
            if asset.asset_type == 'FOREX':
                min_quantity = Decimal('0.01')
                max_quantity = Decimal('10000000')
                tick_size = Decimal('0.00001')
            elif asset.asset_type == 'CRYPTO':
                min_quantity = Decimal('0.00000001')
                max_quantity = Decimal('1000000')
                tick_size = Decimal('0.01')
            else:  # STOCK, COMMODITY, INDEX
                min_quantity = Decimal('0.01')
                max_quantity = Decimal('1000000')
                tick_size = Decimal('0.01')
            
            instrument, created = Instrument.objects.get_or_create(
                symbol=asset.symbol,
                defaults={
                    'name': asset.name,
                    'instrument_type': asset.asset_type,
                    'base_currency': base_currency,
                    'quote_currency': quote_currency,
                    'min_quantity': min_quantity,
                    'max_quantity': max_quantity,
                    'tick_size': tick_size,
                    'is_active': asset.is_active,
                    'legacy_asset_id': asset.id  # Link to legacy asset
                }
            )

            # Update existing instruments with legacy asset ID if not set
            if not created and not instrument.legacy_asset_id:
                instrument.legacy_asset_id = asset.id
                instrument.save()
            
            if created:
                created_count += 1
                self.stdout.write(f"  Created instrument: {instrument.symbol}")
        
        self.stdout.write(
            self.style.SUCCESS(f"Created {created_count} instruments")
        )

    def create_market_data(self):
        """Create sample market data for instruments."""
        self.stdout.write('Creating sample market data...')
        
        instruments = Instrument.objects.filter(is_active=True)
        created_count = 0
        
        # Sample prices for different asset types
        sample_prices = {
            'STOCK': {
                'AAPL': Decimal('150.25'),
                'GOOGL': Decimal('2750.50'),
                'MSFT': Decimal('380.75'),
                'AMZN': Decimal('3200.00'),
                'TSLA': Decimal('250.80'),
            },
            'FOREX': {
                'EUR/USD': Decimal('1.0850'),
                'GBP/USD': Decimal('1.2650'),
                'USD/JPY': Decimal('149.50'),
                'AUD/USD': Decimal('0.6750'),
            },
            'CRYPTO': {
                'BTC/USDT': Decimal('42500.00'),
                'ETH/USDT': Decimal('2650.00'),
                'BNB/USDT': Decimal('320.50'),
                'ADA/USDT': Decimal('0.4850'),
            },
            'COMMODITY': {
                'GOLD': Decimal('2050.00'),
                'SILVER': Decimal('24.50'),
                'OIL': Decimal('78.25'),
            },
            'INDEX': {
                'SPX': Decimal('4750.00'),
                'NASDAQ': Decimal('15200.00'),
                'DOW': Decimal('37500.00'),
            }
        }
        
        for instrument in instruments:
            # Get sample price for this instrument
            base_price = None
            if instrument.instrument_type in sample_prices:
                base_price = sample_prices[instrument.instrument_type].get(instrument.symbol)
            
            if not base_price:
                # Default price based on asset type
                if instrument.instrument_type == 'FOREX':
                    base_price = Decimal('1.0000')
                elif instrument.instrument_type == 'CRYPTO':
                    base_price = Decimal('1000.00')
                else:
                    base_price = Decimal('100.00')
            
            # Create bid/ask spread (0.1% spread)
            spread = base_price * Decimal('0.001')
            bid = base_price - (spread / 2)
            ask = base_price + (spread / 2)
            
            # Create market tick
            tick, created = MarketTick.objects.get_or_create(
                instrument=instrument,
                defaults={
                    'bid': bid,
                    'ask': ask,
                    'last_price': base_price,
                    'volume': Decimal('10000')
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(f"  Created market data for {instrument.symbol}: {base_price}")
        
        self.stdout.write(
            self.style.SUCCESS(f"Created market data for {created_count} instruments")
        )
