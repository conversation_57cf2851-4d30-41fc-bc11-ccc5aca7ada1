"""
Professional trading views for order management and execution.
"""
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db import transaction
from django.utils import timezone
from decimal import Decimal

from .models_professional import Instrument, Order, Execution, Position, MarketTick
from .serializers_professional import (
    InstrumentSerializer, OrderCreateSerializer, OrderSerializer, ExecutionSerializer,
    PositionSerializer, MarketTickSerializer, OrderCancelSerializer, PositionCloseSerializer
)


class InstrumentViewSet(viewsets.ReadOnlyModelViewSet):
    """Instrument viewset for available trading instruments."""
    
    queryset = Instrument.objects.filter(is_active=True)
    serializer_class = InstrumentSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def by_type(self, request):
        """Get instruments grouped by type."""
        instrument_type = request.query_params.get('type')
        if instrument_type:
            instruments = self.queryset.filter(instrument_type=instrument_type)
        else:
            instruments = self.queryset
        
        # Group by type
        grouped = {}
        for instrument in instruments:
            if instrument.instrument_type not in grouped:
                grouped[instrument.instrument_type] = []
            grouped[instrument.instrument_type].append(InstrumentSerializer(instrument).data)
        
        return Response(grouped)


class OrderViewSet(viewsets.ModelViewSet):
    """Order management viewset."""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Return orders for user's trading accounts."""
        return Order.objects.filter(account__user=self.request.user)
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return OrderCreateSerializer
        return OrderSerializer
    
    def create(self, request, *args, **kwargs):
        """Create a new order."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        with transaction.atomic():
            order = serializer.save()
            
            # For market orders, attempt immediate execution
            if order.order_type == 'MARKET':
                self._execute_market_order(order)
        
        return Response(OrderSerializer(order).data, status=status.HTTP_201_CREATED)
    
    def _execute_market_order(self, order):
        """Execute a market order immediately."""
        # Get current market price
        latest_tick = MarketTick.objects.filter(instrument=order.instrument).first()
        if not latest_tick:
            order.status = 'REJECTED'
            order.save()
            return
        
        # Use bid for sell orders, ask for buy orders
        execution_price = latest_tick.ask if order.side == 'BUY' else latest_tick.bid
        
        # Calculate fee (0.1% for example)
        fee_rate = Decimal('0.001')
        notional_value = order.quantity * execution_price
        fee = notional_value * fee_rate
        
        # Create execution record
        execution = Execution.objects.create(
            order=order,
            account=order.account,
            instrument=order.instrument,
            side=order.side,
            price=execution_price,
            quantity=order.quantity,
            fee=fee,
            market_price=latest_tick.last_price
        )
        
        # Update order status
        order.filled_quantity = order.quantity
        order.average_fill_price = execution_price
        order.status = 'FILLED'
        order.save()
        
        # Update account balance and create transaction record
        if order.side == 'BUY':
            # Deduct cost + fee from account
            total_cost = notional_value + fee
            order.account.balance -= total_cost

            # Create transaction record for buy order
            from wallets.models import Transaction
            Transaction.objects.create(
                wallet=order.account.wallet,
                trading_account=order.account,
                transaction_type='TRADE_LOSS',  # Debit for buy
                amount=total_cost,
                status='COMPLETED',
                payment_method='INTERNAL',
                description=f"BUY {order.quantity} {order.instrument.symbol} @ {execution_price}",
                reference=f"BUY_{timezone.now().strftime('%Y%m%d%H%M%S')}"
            )
        else:  # SELL
            # For SELL orders, we need to check if user has the position
            # For now, just add proceeds - fee to account (simplified)
            net_proceeds = notional_value - fee
            order.account.balance += net_proceeds

            # Create transaction record for sell order
            from wallets.models import Transaction
            Transaction.objects.create(
                wallet=order.account.wallet,
                trading_account=order.account,
                transaction_type='TRADE_PROFIT',  # Credit for sell
                amount=net_proceeds,
                status='COMPLETED',
                payment_method='INTERNAL',
                description=f"SELL {order.quantity} {order.instrument.symbol} @ {execution_price}",
                reference=f"SELL_{timezone.now().strftime('%Y%m%d%H%M%S')}"
            )

        order.account.save()
        
        # Create or update position
        self._update_position(order, execution)
    
    def _update_position(self, order, execution):
        """Create or update position based on execution."""
        try:
            # Try to find existing open position
            position = Position.objects.get(
                account=order.account,
                instrument=order.instrument,
                status='OPEN'
            )
            
            # Update existing position
            if order.side == 'BUY':
                # Increase long position or reduce short position
                if position.side == 'LONG':
                    # Add to long position
                    total_quantity = position.quantity + execution.quantity
                    total_cost = (position.entry_price * position.quantity) + (execution.price * execution.quantity)
                    position.entry_price = total_cost / total_quantity
                    position.quantity = total_quantity
                else:  # SHORT position, buying reduces it
                    if execution.quantity >= position.quantity:
                        # Close short position completely
                        position.realized_pnl = (position.entry_price - execution.price) * position.quantity
                        position.status = 'CLOSED'
                        position.closed_at = timezone.now()

                        # If execution quantity > position quantity, create new long position
                        remaining_quantity = execution.quantity - position.quantity
                        if remaining_quantity > 0:
                            Position.objects.create(
                                account=order.account,
                                instrument=order.instrument,
                                side='LONG',
                                entry_price=execution.price,
                                quantity=remaining_quantity,
                                current_price=execution.price,
                                leverage=order.leverage,
                                margin_used=order.margin_required
                            )
                    else:
                        # Partially close short position
                        position.quantity -= execution.quantity
                        position.realized_pnl += (position.entry_price - execution.price) * execution.quantity
            
            else:  # SELL order
                # Similar logic for sell orders...
                if position.side == 'SHORT':
                    # Add to short position
                    total_quantity = position.quantity + execution.quantity
                    total_value = (position.entry_price * position.quantity) + (execution.price * execution.quantity)
                    position.entry_price = total_value / total_quantity
                    position.quantity = total_quantity
                else:  # LONG position, selling reduces it
                    if execution.quantity >= position.quantity:
                        # Close long position completely
                        position.realized_pnl = (execution.price - position.entry_price) * position.quantity
                        position.status = 'CLOSED'
                        position.closed_at = timezone.now()

                        # If execution quantity > position quantity, create new short position
                        remaining_quantity = execution.quantity - position.quantity
                        if remaining_quantity > 0:
                            Position.objects.create(
                                account=order.account,
                                instrument=order.instrument,
                                side='SHORT',
                                entry_price=execution.price,
                                quantity=remaining_quantity,
                                current_price=execution.price,
                                leverage=order.leverage,
                                margin_used=order.margin_required
                            )
                    else:
                        # Partially close long position
                        position.quantity -= execution.quantity
                        position.realized_pnl += (execution.price - position.entry_price) * execution.quantity
            
            position.save()
            
        except Position.DoesNotExist:
            # Create new position
            side = 'LONG' if order.side == 'BUY' else 'SHORT'
            Position.objects.create(
                account=order.account,
                instrument=order.instrument,
                side=side,
                entry_price=execution.price,
                quantity=execution.quantity,
                current_price=execution.price,
                leverage=order.leverage,
                margin_used=order.margin_required
            )
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel an order."""
        order = self.get_object()
        
        if order.account.user != request.user:
            return Response(
                {'error': 'Permission denied'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = OrderCancelSerializer(
            data=request.data, 
            context={'order': order}
        )
        
        if serializer.is_valid():
            order.status = 'CANCELLED'
            order.save()
            
            return Response({
                'message': 'Order cancelled successfully',
                'order': OrderSerializer(order).data
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def open_orders(self, request):
        """Get all open orders for the user."""
        orders = self.get_queryset().filter(status__in=['PENDING', 'PARTIALLY_FILLED'])
        
        page = self.paginate_queryset(orders)
        if page is not None:
            serializer = OrderSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = OrderSerializer(orders, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def close_order(self, request, pk=None):
        """Close an order by creating an opposite order."""
        original_order = self.get_object()

        if original_order.account.user != request.user:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        if original_order.status != 'FILLED':
            return Response(
                {'error': 'Can only close filled orders'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create opposite order to close position
        close_side = 'SELL' if original_order.side == 'BUY' else 'BUY'

        # Get current market price for P&L calculation
        latest_tick = MarketTick.objects.filter(instrument=original_order.instrument).first()
        if not latest_tick:
            return Response(
                {'error': 'No market data available'},
                status=status.HTTP_400_BAD_REQUEST
            )

        current_price = latest_tick.ask if close_side == 'BUY' else latest_tick.bid

        # Calculate P&L
        if original_order.side == 'BUY':
            # Long position: P&L = (current_price - entry_price) * quantity
            pnl = (current_price - original_order.average_fill_price) * original_order.quantity
        else:
            # Short position: P&L = (entry_price - current_price) * quantity
            pnl = (original_order.average_fill_price - current_price) * original_order.quantity

        # Create closing order
        with transaction.atomic():
            close_order = Order.objects.create(
                account=original_order.account,
                instrument=original_order.instrument,
                order_type='MARKET',
                side=close_side,
                quantity=original_order.quantity,
                leverage=1  # No leverage for closing
            )

            # Execute the closing order immediately
            self._execute_market_order(close_order)

            # Update the original order to mark it as closed
            original_order.status = 'CLOSED'
            original_order.save()

        return Response({
            'message': 'Order closed successfully',
            'original_order': OrderSerializer(original_order).data,
            'closing_order': OrderSerializer(close_order).data,
            'pnl': float(pnl),
            'pnl_percentage': float((pnl / (original_order.average_fill_price * original_order.quantity)) * 100),
            'closing_price': float(current_price)
        })


class ExecutionViewSet(viewsets.ReadOnlyModelViewSet):
    """Execution history viewset."""

    serializer_class = ExecutionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Return executions for user's trading accounts."""
        return Execution.objects.filter(account__user=self.request.user)


class PositionViewSet(viewsets.ReadOnlyModelViewSet):
    """Position management viewset."""
    
    serializer_class = PositionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Return positions for user's trading accounts."""
        return Position.objects.filter(account__user=self.request.user)
    
    @action(detail=True, methods=['post'])
    def close(self, request, pk=None):
        """Close a position."""
        position = self.get_object()
        
        if position.account.user != request.user:
            return Response(
                {'error': 'Permission denied'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = PositionCloseSerializer(
            data=request.data, 
            context={'position': position}
        )
        
        if serializer.is_valid():
            # Create market order to close position
            close_side = 'SELL' if position.side == 'LONG' else 'BUY'
            
            order = Order.objects.create(
                account=position.account,
                instrument=position.instrument,
                order_type='MARKET',
                side=close_side,
                quantity=serializer.validated_data['quantity'],
                leverage=1  # No leverage for closing
            )
            
            # Execute the closing order
            with transaction.atomic():
                self._execute_market_order(order)
            
            return Response({
                'message': 'Position close order placed',
                'order': OrderSerializer(order).data
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def open_positions(self, request):
        """Get all open positions for the user."""
        positions = self.get_queryset().filter(status='OPEN')
        
        page = self.paginate_queryset(positions)
        if page is not None:
            serializer = PositionSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = PositionSerializer(positions, many=True)
        return Response(serializer.data)


class MarketDataViewSet(viewsets.ReadOnlyModelViewSet):
    """Market data viewset."""
    
    serializer_class = MarketTickSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Return latest market ticks."""
        return MarketTick.objects.all()
    
    @action(detail=False, methods=['get'])
    def latest_prices(self, request):
        """Get latest prices for all instruments."""
        # Get the latest tick for each instrument
        latest_ticks = {}
        for tick in MarketTick.objects.select_related('instrument').order_by('instrument', '-timestamp').distinct('instrument'):
            latest_ticks[tick.instrument.symbol] = MarketTickSerializer(tick).data
        
        return Response(latest_ticks)
