# Frontend API Guide - Trading Platform

## Overview
Complete API reference for Next.js frontend integration with Django backend.
**Base URL**: `http://localhost:8000/api`

---

## 🔐 Authentication

### Login
**Route**: `POST /auth/login/`
**Payload**: `application/json`
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
**Response**:
```json
{
  "token": "abc123...",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "role": "USER",
    "account_type": "BASIC",
    "is_verified": true
  }
}
```

### Logout
**Route**: `POST /auth/logout/`
**Headers**: `Authorization: Token abc123...`
**Response**: `204 No Content`

### Get Current User
**Route**: `GET /auth/user/`
**Headers**: `Authorization: Token abc123...`
**Response**: User object (same as login)

### Password Reset Request
**Route**: `POST /auth/password-reset-request/`
**Payload**: `application/json`
```json
{
  "email": "<EMAIL>"
}
```

### Password Reset Confirm
**Route**: `POST /auth/password-reset-confirm/`
**Payload**: `application/json`
```json
{
  "token": "reset_token",
  "new_password": "newpassword123"
}
```

---

## 👤 User Management

### Get User Profile
**Route**: `GET /users/{user_id}/`
**Headers**: `Authorization: Token abc123...`
**Response**: User object with full details

### Update User Profile
**Route**: `PATCH /users/{user_id}/`
**Headers**: `Authorization: Token abc123...`
**Payload**: `application/json`
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "phone_number": "+1234567890",
  "address": "123 Main St",
  "date_of_birth": "1990-01-01"
}
```

### Upload Profile Picture
**Route**: `PATCH /users/{user_id}/`
**Headers**: `Authorization: Token abc123...`
**Payload**: `multipart/form-data`
```
profile_picture: [file]
```

---

## 💰 Wallet Management

### List User Wallets
**Route**: `GET /wallets/`
**Headers**: `Authorization: Token abc123...`
**Query Params**: `?page=1&page_size=10`
**Response** (Paginated):
```json
{
  "count": 25,
  "next": "http://localhost:8000/api/wallets/?page=2",
  "previous": null,
  "results": [
    {
      "id": "uuid",
      "name": "USD Wallet",
      "currency": "USD",
      "balance": "1000.********",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### Create Wallet
**Route**: `POST /wallets/`
**Headers**: `Authorization: Token abc123...`
**Payload**: `application/json`
```json
{
  "name": "EUR Wallet",
  "currency": "EUR"
}
```

### Get Wallet Details
**Route**: `GET /wallets/{wallet_id}/`
**Headers**: `Authorization: Token abc123...`

### Update Wallet
**Route**: `PATCH /wallets/{wallet_id}/`
**Headers**: `Authorization: Token abc123...`
**Payload**: `application/json`
```json
{
  "name": "Updated Wallet Name"
}
```

### Transfer from Wallet to Trading Account
**Route**: `POST /wallets/{wallet_id}/transfer_to_account/`
**Headers**: `Authorization: Token abc123...`
**Payload**: `application/json`
```json
{
  "trading_account_id": "account_uuid",
  "amount": "500.00",
  "description": "Fund trading account"
}
```
**Response**:
```json
{
  "message": "Transfer completed successfully",
  "wallet_balance": "1500.********",
  "account_balance": "500.********",
  "amount_transferred": "500.********"
}
```

---

## 🏦 Trading Accounts

### List Trading Accounts
**Route**: `GET /trading-accounts/`
**Headers**: `Authorization: Token abc123...`
**Query Params**: `?page=1&page_size=10`
**Response** (Paginated):
```json
{
  "count": 15,
  "next": "http://localhost:8000/api/trading-accounts/?page=2",
  "previous": null,
  "results": [
    {
      "id": "uuid",
      "account_number": "TA12345678",
      "name": "Main Trading Account",
      "wallet": "wallet_uuid",
      "balance": "0.********",
      "leverage": 1,
      "status": "ACTIVE",
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### Create Trading Account
**Route**: `POST /trading-accounts/`
**Headers**: `Authorization: Token abc123...`
**Payload**: `application/json`
```json
{
  "name": "Crypto Trading Account",
  "wallet_id": "wallet_uuid",
  "leverage": 10
}
```

### Get Account Details
**Route**: `GET /trading-accounts/{account_id}/`
**Headers**: `Authorization: Token abc123...`

### Transfer from Trading Account to Wallet
**Route**: `POST /trading-accounts/{account_id}/transfer_to_wallet/`
**Headers**: `Authorization: Token abc123...`
**Payload**: `application/json`
```json
{
  "amount": "200.00",
  "description": "Withdraw profits to wallet"
}
```
**Response**:
```json
{
  "message": "Transfer completed successfully",
  "account_balance": "300.********",
  "wallet_balance": "1700.********",
  "amount_transferred": "200.********"
}
```

---

## 💸 Transactions (Deposits/Withdrawals)

### List Transactions
**Route**: `GET /transactions/`
**Headers**: `Authorization: Token abc123...`
**Query Params**: `?page=1&page_size=10&wallet_id=uuid&transaction_type=DEPOSIT&status=COMPLETED`
**Response** (Paginated):
```json
{
  "count": 150,
  "next": "http://localhost:8000/api/transactions/?page=2&wallet_id=uuid",
  "previous": null,
  "results": [
    {
      "id": "uuid",
      "wallet": "wallet_uuid",
      "transaction_type": "DEPOSIT",
      "amount": "500.********",
      "status": "COMPLETED",
      "payment_method": "CARD",
      "reference": "TXN123456",
      "description": "Credit card deposit",
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### Create Deposit
**Route**: `POST /transactions/`
**Headers**: `Authorization: Token abc123...`
**Payload**: `application/json`
```json
{
  "wallet_id": "wallet_uuid",
  "transaction_type": "DEPOSIT",
  "amount": "500.00",
  "payment_method": "CARD",
  "description": "Credit card deposit"
}
```

### Create Withdrawal
**Route**: `POST /transactions/`
**Headers**: `Authorization: Token abc123...`
**Payload**: `application/json`
```json
{
  "wallet_id": "wallet_uuid",
  "transaction_type": "WITHDRAWAL",
  "amount": "200.00",
  "payment_method": "BANK_TRANSFER",
  "description": "Bank withdrawal"
}
```

### Get Transaction Details
**Route**: `GET /transactions/{transaction_id}/`
**Headers**: `Authorization: Token abc123...`

---

## � Activities & Audit Trail

### List All Activities
**Route**: `GET /activities/`
**Headers**: `Authorization: Token abc123...`
**Query Params**: `?page=1&page_size=10`
**Response** (Paginated):
```json
{
  "count": 100,
  "next": "http://localhost:8000/api/activities/?page=2",
  "previous": null,
  "results": [
    {
      "id": "uuid",
      "actor": {
        "id": "user_uuid",
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe"
      },
      "target_user": null,
      "activity_type": "USER",
      "description": "User logged in",
      "created_at": "2024-01-01T00:00:00Z",
      "ip_address": "***********"
    }
  ]
}
```

### Get User Activities Only
**Route**: `GET /activities/user_activities/`
**Headers**: `Authorization: Token abc123...`
**Query Params**: `?page=1&page_size=10`
**Response**: Paginated list of user activities (LOGIN, LOGOUT, DEPOSIT, etc.)

### Get Admin Activities Only (Admin Only)
**Route**: `GET /activities/admin_activities/`
**Headers**: `Authorization: Token abc123...`
**Query Params**: `?page=1&page_size=10`
**Response**: Paginated list of admin activities

---

## �📈 Trading Assets

### List All Assets (175 Total)
**Route**: `GET /assets/`
**Headers**: `Authorization: Token abc123...`
**Query Params**: `?page=1&page_size=10&asset_type=STOCK&is_active=true`
**Response** (Paginated):
```json
{
  "count": 175,
  "next": "http://localhost:8000/api/assets/?page=2&asset_type=STOCK",
  "previous": null,
  "results": [
    {
      "id": "uuid",
      "symbol": "AAPL",
      "name": "Apple Inc.",
      "asset_type": "STOCK",
      "description": "Apple Inc. stock",
      "is_active": true,
      "data_source": "ALPHA_VANTAGE",
      "last_price_update": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### Get Asset Details
**Route**: `GET /assets/{asset_id}/`
**Headers**: `Authorization: Token abc123...`

### Get Asset Prices
**Route**: `GET /asset-prices/`
**Headers**: `Authorization: Token abc123...`
**Query Params**: `?page=1&page_size=10&asset_id=uuid&start_date=2024-01-01&end_date=2024-01-07`
**Response** (Paginated):
```json
{
  "count": 500,
  "next": "http://localhost:8000/api/asset-prices/?page=2&asset_id=uuid",
  "previous": null,
  "results": [
    {
      "id": "uuid",
      "asset": "asset_uuid",
      "price": "150.********",
      "open_price": "149.50000000",
      "high_price": "151.********",
      "low_price": "148.75000000",
      "volume": 45234567,
      "change": "0.75000000",
      "change_percent": "0.50%",
      "data_source": "ALPHA_VANTAGE",
      "timestamp": "2024-01-01T00:00:00Z"
    }
  ]
}
```

---

## 📊 Alpha Vantage Real-Time Data

### Get Live Quote
**Route**: `GET /alpha-vantage/live_quote/`
**Headers**: `Authorization: Token abc123...`
**Query Params**: `?symbol=AAPL`
**Response**:
```json
{
  "asset": {
    "symbol": "AAPL",
    "name": "Apple Inc.",
    "asset_type": "STOCK"
  },
  "live_data": {
    "price": "150.25",
    "open": "149.50",
    "high": "151.00",
    "low": "148.75",
    "volume": 45234567,
    "change": "0.75",
    "change_percent": "0.50%"
  },
  "source": "Alpha Vantage",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Get Price History
**Route**: `GET /alpha-vantage/price_history/`
**Headers**: `Authorization: Token abc123...`
**Query Params**: `?symbol=AAPL&days=7`
**Response**: Array of price history data

### Get Supported Assets
**Route**: `GET /alpha-vantage/supported_assets/`
**Headers**: `Authorization: Token abc123...`
**Response**:
```json
{
  "current_assets": {
    "STOCK": [60 assets],
    "FOREX": [29 assets],
    "CRYPTO": [40 assets],
    "COMMODITY": [19 assets],
    "INDEX": [27 assets]
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Manual Price Refresh (Admin Only)
**Route**: `POST /alpha-vantage/refresh_prices/`
**Headers**: `Authorization: Token abc123...`
**Response**:
```json
{
  "message": "Price refresh triggered",
  "task_id": "celery_task_id",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

---

## 🔄 Professional Trading System

### List Available Instruments
**Route**: `GET /instruments/`
**Headers**: `Authorization: Token abc123...`
**Query Params**: `?page=1&page_size=10&instrument_type=STOCK`
**Response** (Paginated):
```json
{
  "count": 175,
  "next": "http://localhost:8000/api/instruments/?page=2",
  "previous": null,
  "results": [
    {
      "id": "uuid",
      "symbol": "AAPL",
      "name": "Apple Inc.",
      "instrument_type": "STOCK",
      "base_currency": "USD",
      "quote_currency": "USD",
      "min_quantity": "0.********",
      "max_quantity": "1000000.********",
      "tick_size": "0.********",
      "is_active": true
    }
  ]
}
```

### Get Instruments by Type
**Route**: `GET /instruments/by_type/?type=STOCK`
**Headers**: `Authorization: Token abc123...`
**Response**: Instruments grouped by type

### Place Order (Professional Trading)
**Route**: `POST /orders/`
**Headers**: `Authorization: Token abc123...`
**Payload**: `application/json`

**Option 1: Using instrument_id (new system)**
```json
{
  "account_id": "account_uuid",
  "instrument_id": "instrument_uuid",
  "order_type": "MARKET",
  "side": "SELL",
  "quantity": "0.1",
  "time_in_force": "GTC",
  "leverage": 1
}
```

**Option 2: Using asset_id (legacy compatibility)**
```json
{
  "account_id": "d6acc81a-fdb6-493a-a49e-6352e48a9921",
  "asset_id": "c6516499-4c6b-43f7-b109-39494c1e5874",
  "order_type": "MARKET",
  "side": "SELL",
  "quantity": "0.1",
  "time_in_force": "GTC",
  "leverage": 1
}
```

**For LIMIT orders, add price:**
```json
{
  "account_id": "account_uuid",
  "asset_id": "asset_uuid",
  "order_type": "LIMIT",
  "side": "BUY",
  "quantity": "100.00",
  "price": "150.25",
  "time_in_force": "GTC",
  "leverage": 1
}
```
**Response**:
```json
{
  "id": "uuid",
  "account": "Main Trading Account",
  "instrument": {
    "symbol": "AAPL",
    "name": "Apple Inc."
  },
  "order_type": "LIMIT",
  "side": "BUY",
  "quantity": "100.********",
  "price": "150.********",
  "status": "PENDING",
  "filled_quantity": "0.********",
  "remaining_quantity": "100.********",
  "fill_percentage": 0.0,
  "created_at": "2024-01-01T00:00:00Z"
}
```

### List Orders
**Route**: `GET /orders/`
**Headers**: `Authorization: Token abc123...`
**Query Params**: `?page=1&page_size=10&status=PENDING`
**Response**: Paginated list of orders

### Get Open Orders
**Route**: `GET /orders/open_orders/`
**Headers**: `Authorization: Token abc123...`
**Response**: List of pending and partially filled orders

### Cancel Order
**Route**: `POST /orders/{order_id}/cancel/`
**Headers**: `Authorization: Token abc123...`
**Payload**: `application/json`
```json
{
  "reason": "User cancelled"
}
```

### Close Order (with P&L calculation)
**Route**: `POST /orders/{order_id}/close_order/`
**Headers**: `Authorization: Token abc123...`
**Payload**: `application/json` (optional)
```json
{
  "reason": "Take profit"
}
```
**Response**:
```json
{
  "message": "Order closed successfully",
  "original_order": {
    "id": "uuid",
    "side": "BUY",
    "quantity": "0.1",
    "average_fill_price": "495.94",
    "status": "CLOSED"
  },
  "closing_order": {
    "id": "uuid",
    "side": "SELL",
    "quantity": "0.1",
    "status": "FILLED"
  },
  "pnl": 5.25,
  "pnl_percentage": 1.06,
  "closing_price": "548.44"
}
```

### List Trade Executions
**Route**: `GET /executions/`
**Headers**: `Authorization: Token abc123...`
**Query Params**: `?page=1&page_size=10`
**Response** (Paginated):
```json
{
  "count": 50,
  "results": [
    {
      "id": "uuid",
      "order": "Order: BUY 100 AAPL @ 150.25",
      "instrument": {
        "symbol": "AAPL",
        "name": "Apple Inc."
      },
      "side": "BUY",
      "price": "150.********",
      "quantity": "100.********",
      "fee": "15.03000000",
      "notional_value": "15025.********",
      "net_amount": "15009.97000000",
      "executed_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### List Positions
**Route**: `GET /positions/`
**Headers**: `Authorization: Token abc123...`
**Query Params**: `?page=1&page_size=10&status=OPEN`
**Response** (Paginated):
```json
{
  "count": 10,
  "results": [
    {
      "id": "uuid",
      "instrument": {
        "symbol": "AAPL",
        "name": "Apple Inc."
      },
      "side": "LONG",
      "entry_price": "150.********",
      "quantity": "100.********",
      "current_price": "152.50000000",
      "unrealized_pnl": "225.********",
      "total_pnl": "225.********",
      "pnl_percentage": 1.5,
      "status": "OPEN",
      "opened_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### Get Open Positions
**Route**: `GET /positions/open_positions/`
**Headers**: `Authorization: Token abc123...`
**Response**: List of open positions

### Close Position
**Route**: `POST /positions/{position_id}/close/`
**Headers**: `Authorization: Token abc123...`
**Payload**: `application/json`
```json
{
  "quantity": "50.00",
  "reason": "Take profit"
}
```

### Get Latest Market Prices
**Route**: `GET /market-data/latest_prices/`
**Headers**: `Authorization: Token abc123...`
**Response**:
```json
{
  "AAPL": {
    "bid": "150.20",
    "ask": "150.25",
    "last_price": "150.22",
    "spread": "0.05",
    "timestamp": "2024-01-01T00:00:00Z"
  },
  "GOOGL": {
    "bid": "2750.50",
    "ask": "2750.75",
    "last_price": "2750.60",
    "spread": "0.25",
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

---

## 🔄 Legacy Trading Operations (Deprecated)

### List User Trades (Legacy)
**Route**: `GET /trades/`
**Note**: This is the legacy simple trading system. Use `/orders/` and `/executions/` for professional trading.

---

## ⚙️ Trading Settings

### Get Trading Settings
**Route**: `GET /trading-settings/`
**Headers**: `Authorization: Token abc123...`
**Query Params**: `?page=1&page_size=10`
**Response** (Paginated):
```json
{
  "count": 25,
  "next": "http://localhost:8000/api/trading-settings/?page=2",
  "previous": null,
  "results": [
    {
      "id": "uuid",
      "asset": "asset_uuid",
      "take_profit_threshold": "5.00",
      "stop_loss_threshold": "5.00",
      "max_leverage": 100,
      "min_leverage": 1,
      "min_trade_amount": "10.00",
      "max_trade_amount": "100000.00",
      "trading_fee_percentage": "0.0020",
      "spread_percentage": "0.0010",
      "is_active": true
    }
  ]
}
```

---

## 📱 Usage Examples

### React Hook for API Calls
```javascript
const useApi = () => {
  const token = localStorage.getItem('token');
  
  const apiCall = async (endpoint, options = {}) => {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}${endpoint}`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
    return response.json();
  };
  
  return { apiCall };
};
```

### Get User Wallets (Paginated)
```javascript
const { apiCall } = useApi();
const walletsResponse = await apiCall('/wallets/?page=1&page_size=10');
const wallets = walletsResponse.results;
const totalCount = walletsResponse.count;
```

### Get All Wallets (All Pages)
```javascript
const getAllWallets = async () => {
  let allWallets = [];
  let page = 1;
  let hasMore = true;

  while (hasMore) {
    const response = await apiCall(`/wallets/?page=${page}&page_size=50`);
    allWallets = [...allWallets, ...response.results];
    hasMore = response.next !== null;
    page++;
  }

  return allWallets;
};
```

### Place a Trade
```javascript
const tradeData = {
  account_id: "account_uuid",
  asset_id: "asset_uuid",
  trade_type: "BUY",
  volume: "100.00",
  leverage: 1
};

const trade = await apiCall('/trades/', {
  method: 'POST',
  body: JSON.stringify(tradeData)
});
```

### Get Live Price (Non-paginated)
```javascript
const livePrice = await apiCall('/alpha-vantage/live_quote/?symbol=AAPL');
```

### Get Assets with Filtering
```javascript
const stocksResponse = await apiCall('/assets/?asset_type=STOCK&page=1&page_size=20');
const stocks = stocksResponse.results;
```

### Transfer Funds Between Wallet and Trading Account
```javascript
// Transfer from wallet to trading account
const transferToAccount = await apiCall(`/wallets/${walletId}/transfer_to_account/`, {
  method: 'POST',
  body: JSON.stringify({
    trading_account_id: accountId,
    amount: "500.00",
    description: "Fund trading account for stock trading"
  })
});

// Transfer from trading account back to wallet
const transferToWallet = await apiCall(`/trading-accounts/${accountId}/transfer_to_wallet/`, {
  method: 'POST',
  body: JSON.stringify({
    amount: "200.00",
    description: "Withdraw profits to wallet"
  })
});
```

---

## 🎯 Asset Categories (175 Total)

- **Stocks (60)**: AAPL, GOOGL, MSFT, AMZN, TSLA, META, NVDA, JPM, V, MA, etc.
- **Forex (29)**: EUR/USD, GBP/USD, USD/JPY, EUR/GBP, AUD/USD, USD/CAD, etc.
- **Crypto (40)**: BTC/USDT, ETH/USDT, BNB/USDT, ADA/USDT, SOL/USDT, DOGE/USDT, etc.
- **Commodities (19)**: GOLD, SILVER, OIL, NATURAL_GAS, COPPER, WHEAT, CORN, etc.
- **Indices (27)**: SPX, NASDAQ, DOW, RUSSELL, DAX, NIKKEI, TECH, FINANCE, etc.

---

## 📄 Pagination

Most list endpoints use **PageNumberPagination** with these parameters:

### Pagination Parameters
- **`page`**: Page number (default: 1)
- **`page_size`**: Items per page (default: 10, can be customized)

### Pagination Response Format
```json
{
  "count": 175,                    // Total number of items
  "next": "http://.../?page=3",    // URL for next page (null if last page)
  "previous": "http://.../?page=1", // URL for previous page (null if first page)
  "results": [...]                 // Array of actual data
}
```

### Paginated Endpoints
- `GET /wallets/` - User wallets
- `GET /trading-accounts/` - Trading accounts
- `GET /transactions/` - Transactions
- `GET /assets/` - All 175 assets
- `GET /asset-prices/` - Price history
- `GET /trades/` - User trades
- `GET /trading-settings/` - Trading settings

### Non-Paginated Endpoints
- `GET /auth/user/` - Current user
- `GET /alpha-vantage/live_quote/` - Live quotes
- `GET /alpha-vantage/supported_assets/` - Asset categories
- Individual item endpoints (`GET /wallets/{id}/`)

---

## 🚨 Important Notes

### Field Naming Convention
When creating resources, use `_id` suffix for foreign key references:
- ✅ `wallet_id` (not `wallet`) - for trading accounts and transactions
- ✅ `account_id` (not `account`) - for trades
- ✅ `asset_id` (not `asset`) - for trades

### Authentication & Errors
- All endpoints require authentication except login and password reset
- Use proper error handling for 401 (Unauthorized), 403 (Forbidden), and 404 (Not Found) responses

### Pagination
- Default page size is 10 items - adjust `page_size` parameter as needed
- Always check `next` field to determine if more pages are available
- For large datasets, consider implementing infinite scroll or pagination controls
